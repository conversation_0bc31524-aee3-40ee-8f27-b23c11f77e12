﻿.ax_default {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
.primary_button {
  color:#FFFFFF;
}
._形状 {
}
._线段 {
}
._图片_ {
  color:#000000;
}
._图片_1 {
}
.placeholder {
}
._一级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._二级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._三级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._四级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._五级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
._六级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
._文本段落 {
  text-align:left;
}
.text_field {
  color:#000000;
  text-align:left;
}
.droplist {
  color:#000000;
  text-align:left;
}
.checkbox {
  text-align:left;
}
.radio_button {
  text-align:left;
}
._流程形状 {
}
._连接 {
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
._表单提示 {
  color:#999999;
}
._表单禁用 {
}
.paragraph {
  text-align:left;
}
.line {
}
.heading_1 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
.horizontal_line {
}
.shape {
}
.box_1 {
}
.box_2 {
}
.box_3 {
}
.ellipse {
}
.image {
  color:#000000;
}
.flow_shape {
}
.iconfont {
  font-family:'iconfont ', 'iconfont', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#666666;
}
.box_31 {
  font-size:12px;
}
.heading_2 {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:20px;
  text-align:left;
  line-height:28px;
}
._形状1 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:normal;
  font-style:normal;
  font-size:13px;
  color:#FFFFFF;
  text-align:center;
  line-height:normal;
}
.shape1 {
  font-family:'Microsoft YaHei ', 'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#333333;
  text-align:left;
  line-height:normal;
}
._默认样式 {
}
._文本段落1 {
  text-align:left;
}
._形状2 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#FF0066;
}
textarea, select, input, button { outline: none; }
