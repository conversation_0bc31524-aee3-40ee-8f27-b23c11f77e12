﻿$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,f),q,_(r,[_(s,t,u,v,w,x,y,z),_(s,A,u,B,w,C,y,A,D,[_(s,E,u,F,w,x,y,G),_(s,H,u,I,w,x,y,J),_(s,K,u,L,w,x,y,M),_(s,N,u,O,w,x,y,P),_(s,Q,u,R,w,x,y,S),_(s,T,u,U,w,x,y,V),_(s,W,u,X,w,x,y,Y)]),_(s,Z,u,ba,w,x,y,bb),_(s,A,u,bc,w,C,y,A,D,[_(s,bd,u,be,w,x,y,bf),_(s,bg,u,bh,w,x,y,bi)])]),bj,[bk,bl,bm],bn,[bo,bp,bq],br,_(bs,A),bt,_(bu,_(s,bv,bw,bx,by,bz,bA,bB,bC,bD,bE,_(bF,bG,bH,bI,bJ,bK),bL,bM,bN,f,bO,bP,bQ,bB,bR,bB,bS,bT,bU,f,bV,_(bW,bX,bY,bX),bZ,_(ca,bX,cb,bX),cc,d,cd,f,ce,bv,cf,_(bF,bG,bH,cg),ch,_(bF,bG,bH,ci),cj,ck,cl,bG,bJ,ck,cm,cn,co,cp,cq,cr,cs,cr,ct,cr,cu,cr,cv,_(),cw,null,cx,null,cy,cn,cz,_(cA,f,cB,cC,cD,cC,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cL)),cM,_(cA,f,cB,bX,cD,cC,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cL)),cN,_(cA,f,cB,bK,cD,bK,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cO)),cP,cQ),cR,_(cS,_(s,cT,bE,_(bF,bG,bH,cg,bJ,bK),cj,cn,cm,bD,cf,_(bF,bG,bH,cU)),cV,_(s,cW),cX,_(s,cY,ch,_(bF,bG,bH,cZ)),da,_(s,db,bE,_(bF,bG,bH,cZ,bJ,bK),cj,cn),dc,_(s,dd,cj,cn),de,_(s,df,cf,_(bF,bG,bH,dg)),dh,_(s,di,bL,dj,by,dk,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),dp,_(s,dq,bL,dr,by,dk,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),ds,_(s,dt,bL,du,by,dk,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),dv,_(s,dw,bL,dx,by,dk,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),dy,_(s,dz,by,dk,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),dA,_(s,dB,bL,dC,by,dk,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),dD,_(s,dE,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),dF,_(s,dG,bE,_(bF,bG,bH,cZ,bJ,bK),bO,dm,co,cp),dH,_(s,dI,bE,_(bF,bG,bH,cZ,bJ,bK),bO,dm,co,dn),dJ,_(s,dK,bO,dm,co,dn),dL,_(s,dM,bO,dm,co,dn),dN,_(s,dO,cf,_(bF,dP,dQ,_(bW,dR,bY,bX),dS,_(bW,dR,bY,bK),dT,[_(bH,cg,dU,bX),_(bH,dg,dU,bX),_(bH,dV,dU,bK),_(bH,cg,dU,bK)])),dW,_(s,dX,ch,_(bF,bG,bH,dY),cj,cr),dZ,_(s,ea,bE,_(bF,bG,bH,eb,bJ,bK)),ec,_(s,ed,cf,_(bF,bG,bH,ee)),ef,_(s,eg,bE,_(bF,bG,bH,eb,bJ,bK)),eh,_(s,ei,cf,_(bF,bG,bH,ee)),ej,_(s,ek,ch,_(bF,bG,bH,dl),cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),el,_(s,em,cf,_(bF,bG,bH,dl)),en,_(s,eo,by,ep,bL,dj,ch,_(bF,bG,bH,dl),cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),eq,_(s,er,bL,dx,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),es,_(s,et,ch,_(bF,bG,bH,cZ)),eu,_(s,ev),ew,_(s,ex),ey,_(s,ez,cj,cn,cf,_(bF,bG,bH,dg)),eA,_(s,eB,cj,cn,cf,_(bF,bG,bH,eC)),eD,_(s,eE),cw,_(s,eF,bE,_(bF,bG,bH,cZ,bJ,bK),cj,cn),eG,_(s,eH,cf,_(bF,dP,dQ,_(bW,dR,bY,bX),dS,_(bW,dR,bY,bK),dT,[_(bH,cg,dU,bX),_(bH,dg,dU,bX),_(bH,dV,dU,bK),_(bH,cg,dU,bK)])),eI,_(s,eJ,bw,eK,by,bz,bA,bB,bC,bD,bE,_(bF,bG,bH,eL,bJ,bK),bL,du,ch,_(bF,bG,bH,dl),cf,_(bF,bG,bH,dl),cq,cn,cs,cn,ct,cn,cu,cn),eM,_(s,eN,bL,eO,cj,cn,cf,_(bF,bG,bH,eC)),eP,_(s,eQ,bw,eR,by,dk,bL,eS,by,dk,cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn,bQ,eT),eU,_(s,eV,bw,bx,by,bB,bA,bB,bC,bD,bE,_(bF,bG,bH,cg,bJ,bK),bL,bM,by,bB,bA,bB,bN,f,ch,_(bF,bG,bH,dg),cj,ck,cl,bG,cm,cn,cf,_(bF,bG,bH,dl),bJ,ck,cz,_(cA,f,cB,cC,cD,cC,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cL)),cM,_(cA,f,cB,bX,cD,cC,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cL)),cN,_(cA,f,cB,bK,cD,bK,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cO)),bO,bP,co,cp,cq,cr,cs,cr,ct,cr,cu,cr,bQ,bB),eW,_(s,eX,bw,eY,by,bz,bA,bB,bC,bD,bE,_(bF,bG,bH,bI,bJ,bK),bL,eO,bN,f,ch,_(bF,bG,bH,eZ),cj,cn,cl,bG,cm,cn,cf,_(bF,bG,bH,bI),bJ,ck,cz,_(cA,f,cB,cC,cD,cC,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cL)),cM,_(cA,f,cB,bX,cD,cC,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cL)),cN,_(cA,f,cB,bK,cD,bK,cE,cC,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,cO)),bO,dm,co,cp,cq,fa,cs,cr,ct,cr,cu,cr,bQ,bB),fb,_(s,fc),fd,_(s,fe,ch,_(bF,bG,bH,dl),cj,cn,cf,_(bF,bG,bH,dl),bO,dm,co,dn,cq,cn,cs,cn,ct,cn,cu,cn),ff,_(s,fg,by,ep,bE,_(bF,bG,bH,fh,bJ,bK),ch,_(bF,bG,bH,dl),cj,cn,cf,_(bF,bG,bH,ci),cz,_(cA,f,cB,bX,cD,bX,cE,fi,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,fj)),cM,_(cA,f,cB,bX,cD,bX,cE,fi,cF,bX,bH,_(cG,cH,cI,cH,cJ,cH,cK,fj)),cq,fk)),fl,_(fm,er,fn,ez,fo,dE,fp,ex,fq,em,fr,eE,fs,cW)));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="sitemap",r="rootNodes",s="id",t="46g78m",u="pageName",v="首页",w="type",x="Wireframe",y="url",z="首页.html",A="",B="工厂循环水系统",C="Folder",D="children",E="cqzgze",F="数字孪生",G="数字孪生.html",H="e18qzf",I="工艺流程",J="工艺流程.html",K="7stpks",L="智能配料",M="智能配料.html",N="ijab3p",O="轨道式",P="轨道式.html",Q="7ipitn",R="多通道",S="多通道.html",T="047atd",U="鱼池清洗",V="鱼池清洗.html",W="3kmzz5",X="AGV调度",Y="agv调度.html",Z="4m0osz",ba="池塘工程化养殖系统",bb="池塘工程化养殖系统.html",bc="撬装式水产养殖系统",bd="do3oua",be="养藻",bf="养藻.html",bg="2cvlhx",bh="多功能水处理",bi="多功能水处理.html",bj="additionalJs",bk="plugins/sitemap/sitemap.js",bl="plugins/page_notes/page_notes.js",bm="plugins/debug/debug.js",bn="additionalCss",bo="plugins/sitemap/styles/sitemap.css",bp="plugins/page_notes/styles/page_notes.css",bq="plugins/debug/styles/debug.css",br="globalVariables",bs="onloadvariable",bt="stylesheet",bu="defaultStyle",bv="627587b6038d43cca051c114ac41ad32",bw="fontName",bx="'Arial Normal', 'Arial', sans-serif",by="fontWeight",bz="400",bA="fontStyle",bB="normal",bC="fontStretch",bD="5",bE="foreGroundFill",bF="fillType",bG="solid",bH="color",bI=0xFF333333,bJ="opacity",bK=1,bL="fontSize",bM="13px",bN="underline",bO="horizontalAlignment",bP="center",bQ="lineSpacing",bR="characterSpacing",bS="letterCase",bT="none",bU="strikethrough",bV="location",bW="x",bX=0,bY="y",bZ="size",ca="width",cb="height",cc="visible",cd="limbo",ce="baseStyle",cf="fill",cg=0xFFFFFFFF,ch="borderFill",ci=0xFF797979,cj="borderWidth",ck="1",cl="linePattern",cm="cornerRadius",cn="0",co="verticalAlignment",cp="middle",cq="paddingLeft",cr="2",cs="paddingTop",ct="paddingRight",cu="paddingBottom",cv="stateStyles",cw="image",cx="imageFilter",cy="rotation",cz="outerShadow",cA="on",cB="offsetX",cC=5,cD="offsetY",cE="blurRadius",cF="spread",cG="r",cH=0,cI="g",cJ="b",cK="a",cL=0.349019607843137,cM="innerShadow",cN="textShadow",cO=0.647058823529412,cP="viewOverride",cQ="19e82109f102476f933582835c373474",cR="customStyles",cS="primary_button",cT="cd64754845384de3872fb4a066432c1f",cU=0xFF169BD5,cV="_形状",cW="40519e9ec4264601bfb12c514e4f4867",cX="_线段",cY="1f539602ad3b499fb26433d0bae24702",cZ=0xFF000000,da="_图片_",db="45e8145a607c494aafde4a6665ecb3c1",dc="_图片_1",dd="75a91ee5b9d042cfa01b8d565fe289c0",de="placeholder",df="c50e74f669b24b37bd9c18da7326bccd",dg=0xFFF2F2F2,dh="_一级标题",di="1111111151944dfba49f67fd55eb1f88",dj="32px",dk="bold",dl=0xFFFFFF,dm="left",dn="top",dp="_二级标题",dq="b3a15c9ddde04520be40f94c8168891e",dr="24px",ds="_三级标题",dt="8c7a4c5ad69a4369a5f7788171ac0b32",du="18px",dv="_四级标题",dw="e995c891077945c89c0b5fe110d15a0b",dx="14px",dy="_五级标题",dz="386b19ef4be143bd9b6c392ded969f89",dA="_六级标题",dB="fc3b9a13b5574fa098ef0a1db9aac861",dC="10px",dD="_文本段落",dE="4988d43d80b44008a4a415096f1632af",dF="text_field",dG="44157808f2934100b68f2394a66b2bba",dH="droplist",dI="85f724022aae41c594175ddac9c289eb",dJ="checkbox",dK="********************************",dL="radio_button",dM="4eb5516f311c4bdfa0cb11d7ea75084e",dN="_流程形状",dO="df01900e3c4e43f284bafec04b0864c4",dP="linearGradient",dQ="startPoint",dR=0.5,dS="endPoint",dT="stops",dU="offset",dV=0xFFE4E4E4,dW="_连接",dX="699a012e142a4bcba964d96e88b88bdf",dY=0xFF0099CC,dZ="form_hint",ea="3c35f7f584574732b5edbd0cff195f77",eb=0xFF999999,ec="form_disabled",ed="2829faada5f8449da03773b96e566862",ee=0xFFF0F0F0,ef="_表单提示",eg="4889d666e8ad4c5e81e59863039a5cc0",eh="_表单禁用",ei="9bd0236217a94d89b0314c8c7fc75f16",ej="paragraph",ek="e0621db17f4b42e0bd8f63006e6cfe5b",el="line",em="12e63bf1ccc1446488aa09e9482180bc",en="heading_1",eo="922caedbf2d2483e8cf0bbbc50ba6e04",ep="700",eq="label",er="e3de336e31594a60bc0966351496a9ce",es="horizontal_line",et="75a015e95a484881b32de65ff86808a9",eu="shape",ev="96fe18664bb44d8fb1e2f882b7f9a01e",ew="box_1",ex="********************************",ey="box_2",ez="********************************",eA="box_3",eB="********************************",eC=0xFFD7D7D7,eD="ellipse",eE="6378b734cecb4b279bccd7e81849e2e3",eF="ca4260183c2644a8a871aab076cc5343",eG="flow_shape",eH="6eba2bf93a60425488ac5850e29872d7",eI="iconfont",eJ="f094e831ba764d0a99029dfb831cf97d",eK="'iconfont ', 'iconfont', sans-serif",eL=0xFF666666,eM="box_31",eN="********************************",eO="12px",eP="heading_2",eQ="fcea98ed953442ba93becac780971aa6",eR="'PingFang SC ', 'PingFang SC', sans-serif",eS="20px",eT="28px",eU="_形状1",eV="7b82b97cfb8a4cb2a4570b0587e8a892",eW="shape1",eX="d907f1336bad4625b7089f7a28a0570b",eY="'Microsoft YaHei ', 'Microsoft YaHei', sans-serif",eZ=0xFFDDDDDD,fa="10",fb="_默认样式",fc="46c253d7724a475ab47861787e2457c6",fd="_文本段落1",fe="bdd31f04b0da422098d64ef8fbab1bd5",ff="_形状2",fg="2625fd73bf4e4b64a69993ccf3bf554c",fh=0xFFFF0066,fi=10,fj=0.313725490196078,fk="20",fl="duplicateStyles",fm="2285372321d148ec80932747449c36c9",fn="47641f9a00ac465095d6b672bbdffef6",fo="96fc5c8eefa948e38813ad85bb35602a",fp="4b7bfc596114427989e10bb0b557d0ce",fq="619b2148ccc1497285562264d51992f9",fr="eff044fe6497434a8c5f89f769ddde3b",fs="a825586033244deeb82caedd2bb4ff5f";
return _creator();
})());