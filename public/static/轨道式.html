﻿<!DOCTYPE html>
<html>
  <head>
    <title>轨道式</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/轨道式/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/轨道式/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (组合) -->
      <div id="u591" class="ax_default" data-left="2" data-top="0" data-width="1920" data-height="1080">

        <!-- Unnamed (矩形) -->
        <div id="u592" class="ax_default box_2">
          <div id="u592_div" class=""></div>
          <div id="u592_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u593" class="ax_default _图片_1">
        <img id="u593_img" class="img " src="images/首页/u215.png"/>
        <div id="u593_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u594" class="ax_default" data-left="24" data-top="13" data-width="252" data-height="45">

        <!-- Unnamed (组合) -->
        <div id="u595" class="ax_default" data-left="24" data-top="13" data-width="252" data-height="45">

          <!-- Unnamed (矩形) -->
          <div id="u596" class="ax_default _文本段落">
            <div id="u596_div" class=""></div>
            <div id="u596_text" class="text ">
              <p><span>轨道式投饵机器人</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (导航) -->

      <!-- Unnamed (组合) -->
      <div id="u598" class="ax_default" data-left="568" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u599" class="ax_default box_1">
          <img id="u599_img" class="img " src="images/首页/u218.svg"/>
          <div id="u599_text" class="text ">
            <p><span>工厂化循环水系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u600" class="ax_default" data-left="752" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u601" class="ax_default box_1">
          <img id="u601_img" class="img " src="images/首页/u218.svg"/>
          <div id="u601_text" class="text ">
            <p><span>池塘工程化养殖系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u602" class="ax_default" data-left="936" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u603" class="ax_default box_1">
          <img id="u603_img" class="img " src="images/首页/u218.svg"/>
          <div id="u603_text" class="text ">
            <p><span>撬装式水产养殖系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u604" class="ax_default box_1">
        <img id="u604_img" class="img " src="images/首页/u223.svg"/>
        <div id="u604_text" class="text ">
          <p><span>生产管理系统</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u605" class="ax_default box_1">
        <img id="u605_img" class="img " src="images/首页/u224.svg"/>
        <div id="u605_text" class="text ">
          <p><span>能力中心</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u606" class="ax_default box_1">
        <img id="u606_img" class="img " src="images/首页/u223.svg"/>
        <div id="u606_text" class="text ">
          <p><span>生产示范基地</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u607" class="ax_default box_1">
        <img id="u607_img" class="img " src="images/首页/u226.svg"/>
        <div id="u607_text" class="text ">
          <p><span>数据中心</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u608" class="ax_default box_1">
        <div id="u608_div" class=""></div>
        <div id="u608_text" class="text ">
          <p><span>系统管理员&nbsp; |&nbsp; 退出</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u609" class="ax_default box_1">
        <img id="u609_img" class="img " src="images/首页/u228.svg"/>
        <div id="u609_text" class="text ">
          <p><span>首页</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u610" class="ax_default _图片_1">
        <img id="u610_img" class="img " src="images/首页/u229.png"/>
        <div id="u610_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 二级1 (组合) -->
      <div id="u611" class="ax_default ax_default_hidden" data-label="二级1" style="display:none; visibility: hidden" data-left="578" data-top="62" data-width="153" data-height="360">

        <!-- Unnamed (矩形) -->
        <div id="u612" class="ax_default box_1">
          <img id="u612_img" class="img " src="images/首页/u231.svg"/>
          <div id="u612_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u613" class="ax_default _文本段落">
          <div id="u613_div" class=""></div>
          <div id="u613_text" class="text ">
            <p><span>数字孪生平台</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u614" class="ax_default _文本段落">
          <div id="u614_div" class=""></div>
          <div id="u614_text" class="text ">
            <p><span>工艺流程图</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u615" class="ax_default _文本段落">
          <div id="u615_div" class=""></div>
          <div id="u615_text" class="text ">
            <p><span>智能配料系统</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u616" class="ax_default _文本段落">
          <div id="u616_div" class=""></div>
          <div id="u616_text" class="text ">
            <p><span>轨道式投饵机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u617" class="ax_default _文本段落">
          <div id="u617_div" class=""></div>
          <div id="u617_text" class="text ">
            <p><span>多通道投饵机</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u618" class="ax_default _文本段落">
          <div id="u618_div" class=""></div>
          <div id="u618_text" class="text ">
            <p><span>鱼池清洗机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u619" class="ax_default _文本段落">
          <div id="u619_div" class=""></div>
          <div id="u619_text" class="text ">
            <p><span>巡检机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u620" class="ax_default _文本段落">
          <div id="u620_div" class=""></div>
          <div id="u620_text" class="text ">
            <p><span>AGV调度</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u621" class="ax_default _文本段落">
          <div id="u621_div" class=""></div>
          <div id="u621_text" class="text ">
            <p><span>溶解氧精准控制系统</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u622" class="ax_default _文本段落">
          <div id="u622_div" class=""></div>
          <div id="u622_text" class="text ">
            <p><span>分级计数设备</span></p>
          </div>
        </div>
      </div>

      <!-- 二级2 (组合) -->
      <div id="u623" class="ax_default ax_default_hidden" data-label="二级2" style="display:none; visibility: hidden" data-left="947" data-top="62" data-width="153" data-height="108">

        <!-- Unnamed (矩形) -->
        <div id="u624" class="ax_default box_1">
          <img id="u624_img" class="img " src="images/首页/u243.svg"/>
          <div id="u624_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u625" class="ax_default _文本段落">
          <div id="u625_div" class=""></div>
          <div id="u625_text" class="text ">
            <p><span>养藻</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u626" class="ax_default _文本段落">
          <div id="u626_div" class=""></div>
          <div id="u626_text" class="text ">
            <p><span>多功能水处理</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u627" class="ax_default _文本段落">
          <div id="u627_div" class=""></div>
          <div id="u627_text" class="text ">
            <p><span>循环水养殖集装箱</span></p>
          </div>
        </div>
      </div>

      <!-- 二级3 (组合) -->
      <div id="u628" class="ax_default ax_default_hidden" data-label="二级3" style="display:none; visibility: hidden" data-left="1345" data-top="62" data-width="153" data-height="72">

        <!-- Unnamed (矩形) -->
        <div id="u629" class="ax_default box_1">
          <img id="u629_img" class="img " src="images/首页/u248.svg"/>
          <div id="u629_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u630" class="ax_default _文本段落">
          <div id="u630_div" class=""></div>
          <div id="u630_text" class="text ">
            <p><span>高新区鱼菜共生AI工厂</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u631" class="ax_default _文本段落">
          <div id="u631_div" class=""></div>
          <div id="u631_text" class="text ">
            <p><span>梁平区鱼菜共生AI工厂</span></p>
          </div>
        </div>
      </div>
      <div id="u597" style="display:none; visibility:hidden;"></div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
