﻿<!DOCTYPE html>
<html>
  <head>
    <title>AGV调度</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/agv调度/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/agv调度/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (组合) -->
      <div id="u714" class="ax_default" data-left="2" data-top="0" data-width="1920" data-height="1080">

        <!-- Unnamed (矩形) -->
        <div id="u715" class="ax_default box_2">
          <div id="u715_div" class=""></div>
          <div id="u715_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u716" class="ax_default _图片_1">
        <img id="u716_img" class="img " src="images/首页/u215.png"/>
        <div id="u716_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u717" class="ax_default" data-left="24" data-top="13" data-width="127" data-height="45">

        <!-- Unnamed (组合) -->
        <div id="u718" class="ax_default" data-left="24" data-top="13" data-width="127" data-height="45">

          <!-- Unnamed (矩形) -->
          <div id="u719" class="ax_default _文本段落">
            <div id="u719_div" class=""></div>
            <div id="u719_text" class="text ">
              <p><span>AGV调度</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (导航) -->

      <!-- Unnamed (组合) -->
      <div id="u721" class="ax_default" data-left="568" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u722" class="ax_default box_1">
          <img id="u722_img" class="img " src="images/首页/u218.svg"/>
          <div id="u722_text" class="text ">
            <p><span>工厂化循环水系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u723" class="ax_default" data-left="752" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u724" class="ax_default box_1">
          <img id="u724_img" class="img " src="images/首页/u218.svg"/>
          <div id="u724_text" class="text ">
            <p><span>池塘工程化养殖系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u725" class="ax_default" data-left="936" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u726" class="ax_default box_1">
          <img id="u726_img" class="img " src="images/首页/u218.svg"/>
          <div id="u726_text" class="text ">
            <p><span>撬装式水产养殖系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u727" class="ax_default box_1">
        <img id="u727_img" class="img " src="images/首页/u223.svg"/>
        <div id="u727_text" class="text ">
          <p><span>生产管理系统</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u728" class="ax_default box_1">
        <img id="u728_img" class="img " src="images/首页/u224.svg"/>
        <div id="u728_text" class="text ">
          <p><span>能力中心</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u729" class="ax_default box_1">
        <img id="u729_img" class="img " src="images/首页/u223.svg"/>
        <div id="u729_text" class="text ">
          <p><span>生产示范基地</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u730" class="ax_default box_1">
        <img id="u730_img" class="img " src="images/首页/u226.svg"/>
        <div id="u730_text" class="text ">
          <p><span>数据中心</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u731" class="ax_default box_1">
        <div id="u731_div" class=""></div>
        <div id="u731_text" class="text ">
          <p><span>系统管理员&nbsp; |&nbsp; 退出</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u732" class="ax_default box_1">
        <img id="u732_img" class="img " src="images/首页/u228.svg"/>
        <div id="u732_text" class="text ">
          <p><span>首页</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u733" class="ax_default _图片_1">
        <img id="u733_img" class="img " src="images/首页/u229.png"/>
        <div id="u733_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 二级1 (组合) -->
      <div id="u734" class="ax_default ax_default_hidden" data-label="二级1" style="display:none; visibility: hidden" data-left="578" data-top="62" data-width="153" data-height="360">

        <!-- Unnamed (矩形) -->
        <div id="u735" class="ax_default box_1">
          <img id="u735_img" class="img " src="images/首页/u231.svg"/>
          <div id="u735_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u736" class="ax_default _文本段落">
          <div id="u736_div" class=""></div>
          <div id="u736_text" class="text ">
            <p><span>数字孪生平台</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u737" class="ax_default _文本段落">
          <div id="u737_div" class=""></div>
          <div id="u737_text" class="text ">
            <p><span>工艺流程图</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u738" class="ax_default _文本段落">
          <div id="u738_div" class=""></div>
          <div id="u738_text" class="text ">
            <p><span>智能配料系统</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u739" class="ax_default _文本段落">
          <div id="u739_div" class=""></div>
          <div id="u739_text" class="text ">
            <p><span>轨道式投饵机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u740" class="ax_default _文本段落">
          <div id="u740_div" class=""></div>
          <div id="u740_text" class="text ">
            <p><span>多通道投饵机</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u741" class="ax_default _文本段落">
          <div id="u741_div" class=""></div>
          <div id="u741_text" class="text ">
            <p><span>鱼池清洗机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u742" class="ax_default _文本段落">
          <div id="u742_div" class=""></div>
          <div id="u742_text" class="text ">
            <p><span>巡检机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u743" class="ax_default _文本段落">
          <div id="u743_div" class=""></div>
          <div id="u743_text" class="text ">
            <p><span>AGV调度</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u744" class="ax_default _文本段落">
          <div id="u744_div" class=""></div>
          <div id="u744_text" class="text ">
            <p><span>溶解氧精准控制系统</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u745" class="ax_default _文本段落">
          <div id="u745_div" class=""></div>
          <div id="u745_text" class="text ">
            <p><span>分级计数设备</span></p>
          </div>
        </div>
      </div>

      <!-- 二级2 (组合) -->
      <div id="u746" class="ax_default ax_default_hidden" data-label="二级2" style="display:none; visibility: hidden" data-left="947" data-top="62" data-width="153" data-height="108">

        <!-- Unnamed (矩形) -->
        <div id="u747" class="ax_default box_1">
          <img id="u747_img" class="img " src="images/首页/u243.svg"/>
          <div id="u747_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u748" class="ax_default _文本段落">
          <div id="u748_div" class=""></div>
          <div id="u748_text" class="text ">
            <p><span>养藻</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u749" class="ax_default _文本段落">
          <div id="u749_div" class=""></div>
          <div id="u749_text" class="text ">
            <p><span>多功能水处理</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u750" class="ax_default _文本段落">
          <div id="u750_div" class=""></div>
          <div id="u750_text" class="text ">
            <p><span>循环水养殖集装箱</span></p>
          </div>
        </div>
      </div>

      <!-- 二级3 (组合) -->
      <div id="u751" class="ax_default ax_default_hidden" data-label="二级3" style="display:none; visibility: hidden" data-left="1345" data-top="62" data-width="153" data-height="72">

        <!-- Unnamed (矩形) -->
        <div id="u752" class="ax_default box_1">
          <img id="u752_img" class="img " src="images/首页/u248.svg"/>
          <div id="u752_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u753" class="ax_default _文本段落">
          <div id="u753_div" class=""></div>
          <div id="u753_text" class="text ">
            <p><span>高新区鱼菜共生AI工厂</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u754" class="ax_default _文本段落">
          <div id="u754_div" class=""></div>
          <div id="u754_text" class="text ">
            <p><span>梁平区鱼菜共生AI工厂</span></p>
          </div>
        </div>
      </div>
      <div id="u720" style="display:none; visibility:hidden;"></div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
