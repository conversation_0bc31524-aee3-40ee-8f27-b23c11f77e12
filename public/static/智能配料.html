﻿<!DOCTYPE html>
<html>
  <head>
    <title>智能配料</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/智能配料/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/智能配料/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (组合) -->
      <div id="u550" class="ax_default" data-left="2" data-top="0" data-width="1920" data-height="1080">

        <!-- Unnamed (矩形) -->
        <div id="u551" class="ax_default box_2">
          <div id="u551_div" class=""></div>
          <div id="u551_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u552" class="ax_default _图片_1">
        <img id="u552_img" class="img " src="images/首页/u215.png"/>
        <div id="u552_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u553" class="ax_default" data-left="24" data-top="13" data-width="189" data-height="45">

        <!-- Unnamed (组合) -->
        <div id="u554" class="ax_default" data-left="24" data-top="13" data-width="189" data-height="45">

          <!-- Unnamed (矩形) -->
          <div id="u555" class="ax_default _文本段落">
            <div id="u555_div" class=""></div>
            <div id="u555_text" class="text ">
              <p><span>智能配料系统</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (导航) -->

      <!-- Unnamed (组合) -->
      <div id="u557" class="ax_default" data-left="568" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u558" class="ax_default box_1">
          <img id="u558_img" class="img " src="images/首页/u218.svg"/>
          <div id="u558_text" class="text ">
            <p><span>工厂化循环水系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u559" class="ax_default" data-left="752" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u560" class="ax_default box_1">
          <img id="u560_img" class="img " src="images/首页/u218.svg"/>
          <div id="u560_text" class="text ">
            <p><span>池塘工程化养殖系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u561" class="ax_default" data-left="936" data-top="13" data-width="172" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u562" class="ax_default box_1">
          <img id="u562_img" class="img " src="images/首页/u218.svg"/>
          <div id="u562_text" class="text ">
            <p><span>撬装式水产养殖系统</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u563" class="ax_default box_1">
        <img id="u563_img" class="img " src="images/首页/u223.svg"/>
        <div id="u563_text" class="text ">
          <p><span>生产管理系统</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u564" class="ax_default box_1">
        <img id="u564_img" class="img " src="images/首页/u224.svg"/>
        <div id="u564_text" class="text ">
          <p><span>能力中心</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u565" class="ax_default box_1">
        <img id="u565_img" class="img " src="images/首页/u223.svg"/>
        <div id="u565_text" class="text ">
          <p><span>生产示范基地</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u566" class="ax_default box_1">
        <img id="u566_img" class="img " src="images/首页/u226.svg"/>
        <div id="u566_text" class="text ">
          <p><span>数据中心</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u567" class="ax_default box_1">
        <div id="u567_div" class=""></div>
        <div id="u567_text" class="text ">
          <p><span>系统管理员&nbsp; |&nbsp; 退出</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u568" class="ax_default box_1">
        <img id="u568_img" class="img " src="images/首页/u228.svg"/>
        <div id="u568_text" class="text ">
          <p><span>首页</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u569" class="ax_default _图片_1">
        <img id="u569_img" class="img " src="images/首页/u229.png"/>
        <div id="u569_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 二级1 (组合) -->
      <div id="u570" class="ax_default ax_default_hidden" data-label="二级1" style="display:none; visibility: hidden" data-left="578" data-top="62" data-width="153" data-height="360">

        <!-- Unnamed (矩形) -->
        <div id="u571" class="ax_default box_1">
          <img id="u571_img" class="img " src="images/首页/u231.svg"/>
          <div id="u571_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u572" class="ax_default _文本段落">
          <div id="u572_div" class=""></div>
          <div id="u572_text" class="text ">
            <p><span>数字孪生平台</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u573" class="ax_default _文本段落">
          <div id="u573_div" class=""></div>
          <div id="u573_text" class="text ">
            <p><span>工艺流程图</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u574" class="ax_default _文本段落">
          <div id="u574_div" class=""></div>
          <div id="u574_text" class="text ">
            <p><span>智能配料系统</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u575" class="ax_default _文本段落">
          <div id="u575_div" class=""></div>
          <div id="u575_text" class="text ">
            <p><span>轨道式投饵机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u576" class="ax_default _文本段落">
          <div id="u576_div" class=""></div>
          <div id="u576_text" class="text ">
            <p><span>多通道投饵机</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u577" class="ax_default _文本段落">
          <div id="u577_div" class=""></div>
          <div id="u577_text" class="text ">
            <p><span>鱼池清洗机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u578" class="ax_default _文本段落">
          <div id="u578_div" class=""></div>
          <div id="u578_text" class="text ">
            <p><span>巡检机器人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u579" class="ax_default _文本段落">
          <div id="u579_div" class=""></div>
          <div id="u579_text" class="text ">
            <p><span>AGV调度</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u580" class="ax_default _文本段落">
          <div id="u580_div" class=""></div>
          <div id="u580_text" class="text ">
            <p><span>溶解氧精准控制系统</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u581" class="ax_default _文本段落">
          <div id="u581_div" class=""></div>
          <div id="u581_text" class="text ">
            <p><span>分级计数设备</span></p>
          </div>
        </div>
      </div>

      <!-- 二级2 (组合) -->
      <div id="u582" class="ax_default ax_default_hidden" data-label="二级2" style="display:none; visibility: hidden" data-left="947" data-top="62" data-width="153" data-height="108">

        <!-- Unnamed (矩形) -->
        <div id="u583" class="ax_default box_1">
          <img id="u583_img" class="img " src="images/首页/u243.svg"/>
          <div id="u583_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u584" class="ax_default _文本段落">
          <div id="u584_div" class=""></div>
          <div id="u584_text" class="text ">
            <p><span>养藻</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u585" class="ax_default _文本段落">
          <div id="u585_div" class=""></div>
          <div id="u585_text" class="text ">
            <p><span>多功能水处理</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u586" class="ax_default _文本段落">
          <div id="u586_div" class=""></div>
          <div id="u586_text" class="text ">
            <p><span>循环水养殖集装箱</span></p>
          </div>
        </div>
      </div>

      <!-- 二级3 (组合) -->
      <div id="u587" class="ax_default ax_default_hidden" data-label="二级3" style="display:none; visibility: hidden" data-left="1345" data-top="62" data-width="153" data-height="72">

        <!-- Unnamed (矩形) -->
        <div id="u588" class="ax_default box_1">
          <img id="u588_img" class="img " src="images/首页/u248.svg"/>
          <div id="u588_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u589" class="ax_default _文本段落">
          <div id="u589_div" class=""></div>
          <div id="u589_text" class="text ">
            <p><span>高新区鱼菜共生AI工厂</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u590" class="ax_default _文本段落">
          <div id="u590_div" class=""></div>
          <div id="u590_text" class="text ">
            <p><span>梁平区鱼菜共生AI工厂</span></p>
          </div>
        </div>
      </div>
      <div id="u556" style="display:none; visibility:hidden;"></div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
