import { ReqPage, ResPage } from "@/api/interface";

// 资产管理
export namespace Cmdb {
  // 服务器
  export interface Accounts$1Type {
    id?: number;
    server?: number;
    username: string;
    password: string;
    port: number;
  }

  export interface Server$2Type {
    id?: number;
    accounts: Array<Accounts$1Type>;
    asset?: number;
    server_type?: string;
    server_system_type: number | null;
    model: string;
    use: string;
    server_type_display?: string;
    server_system_type_display?: string;
  }

  export interface ResServersItem {
    id: number;
    asset_type: string;
    server: Server$2Type;
    create_time: string;
    update_time: string;
    name: string;
    sn: string;
    asset_status: number | null;
    manage_ip: string;
    expire_day: string;
    memo: string;
    department: any;
    admin: number | null;
    cabinet: any;
    asset_type_display: string;
    asset_status_display: string;
    department_display: string;
    admin_display: string;
    cabinet_display: string;
    idc: string;
  }

  export type ResServers = ResPage<ResServersItem>;

  // export type ReqCmdb = ReqPage;
  export interface ReqServers extends ReqPage {
    id: number;
    asset_type: string;
    server: Server$2Type;
    create_time: string;
    update_time: string;
    name: string;
    sn: string;
    asset_status?: number;
    manage_ip: string;
    expire_day?: string;
    memo: string;
    department: any;
    admin?: number;
    cabinet?: any;
    asset_type_display: string;
    asset_status_display: string;
    department_display: string;
    admin_display: string;
    cabinet_display: string;
    idc: string;
  }
}
