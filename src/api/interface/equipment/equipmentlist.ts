import { ReqPage, ResPage } from "@/api/interface";

// 设备列表
export namespace Equipment {
  // 设备管理
  export interface ResEquipmentlistItem {
    id: number;
    name: string; // 名称
    type: string; // 类型
    serial_number: string; // 序列号
    purchase_date: string; // 购买日期
    fishfarmsystem: number; // 养鱼系统
    qr_code: any; // 二维码
  }

  export type ResEquipmentlist = ResPage<ResEquipmentlistItem>;

  export interface ReqEquipmentlistParam extends ReqPage {
    id: number;
    name: string; // 名称
    type: string; // 类型
    serial_number: string; // 序列号
    purchase_date: string; // 购买日期
    fishfarmsystem: number; // 养鱼系统
  }

  export interface SensorData {
    id: number;
    name: string;
    values: {
      oxygen: number;
      temperature: number;
      liquid: number;
    };
  }
}
