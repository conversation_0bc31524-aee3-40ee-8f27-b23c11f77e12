import { ReqPage, ResPage } from "@/api/interface";

// 运营成本
export namespace CostMgmt {
  // 运营成本管理
  export interface ResOperatingcostsItem {
    id: number;
    money: number; // 金额
    remark: string; // 备注
    costcat: number; // 费用分类表
  }

  export type ResOperatingcosts = ResPage<ResOperatingcostsItem>;

  export interface ReqOperatingcostsParam extends ReqPage {
    id: number;
    money: number; // 金额
    remark: string; // 备注
    costcat: number; // 费用分类表
  }
}
