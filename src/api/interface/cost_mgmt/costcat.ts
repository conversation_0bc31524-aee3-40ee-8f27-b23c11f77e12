import { ReqPage, ResPage } from "@/api/interface";

// 费用分类
export namespace CostMgmt {
  // 运营成本管理
  export interface ResCostcatTree {
    id: number;
    name: string; // 类别名称
    remark: string; // 备注
    costcat: number; // 父级
    children?: ResCostcatTree;
  }

  export type ResCostcat = ResPage<ResCostcatTree>;

  export interface ReqCostcatParam extends ReqPage {
    id: number;
    name: string; // 类别名称
    remark: string; // 备注
    costcat: number; // 父级
  }

  export interface CostcatNameTree {
    id?: number;
    name?: string;
    costcat?: number;
    children?: CostcatNameTree;
  }
}
