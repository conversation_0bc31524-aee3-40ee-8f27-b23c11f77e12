import { ReqPage, ResPage } from "@/api/interface";

// 测试1张
export namespace Avg {
  // 测试一下
  export interface ResAccessItem {
    id: number;
    name: string; // 名字
    file_type: string; // 类型
    file_url: string; // 地址
  }

  export type ResAccess = ResPage<ResAccessItem>;

  export interface ReqAccessParam extends ReqPage {
    id: number;
    name: string; // 名字
    file_type: string; // 类型
    file_url: string; // 地址
  }
}
