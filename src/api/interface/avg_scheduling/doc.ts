import { ReqPage, ResPage } from "@/api/interface";

// AVG调度-文档列表
export namespace AvgSchedulingDoc {
  // 文档列表
  export interface ResAvgSchedulingDocItem {
    id: number;
    name: string; // 文件名称
    type: string; // 文档类别
    file_url: string; // 文档地址
  }

  export type ResAvgSchedulingDocList = ResPage<ResAvgSchedulingDocItem>;

  export interface ReqAvgSchedulingDocList extends ReqPage {
    name: string; // 文件名称
    file_type: string; // 文档类别
  }

  export interface DictOption {
    label: string;
    value: string;
  }
}
