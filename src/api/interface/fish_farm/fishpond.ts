import { ReqPage, ResPage } from "@/api/interface";

// 鱼池状态
export namespace FishFarm {
  // 养殖管理
  export interface ResFishpondItem {
    id: number;
    fishfarmsystem: number; // 养鱼系统
    name: string; // 鱼池名称
    status: boolean; // 鱼池状态
    quantity: number; // 鱼数量
    batch_id: string; // 批次ID
    current_status: boolean; // 当前状态
    location: string; // 位置
    qr_code: any; // 二维码
  }

  export type ResFishpond = ResPage<ResFishpondItem>;

  export interface ReqFishpondParam extends ReqPage {
    id: number;
    fishfarmsystem: number; // 养鱼系统
    name: string; // 鱼池名称
    status: boolean; // 鱼池状态
    quantity: number; // 鱼数量
    batch_id: string; // 批次ID
    current_status: boolean; // 当前状态
    location: string; // 位置
  }
}
