import { ReqPage, ResPage } from "@/api/interface";

// 进出记录
export namespace FishFarm {
  // 养殖管理
  export interface ResInoutrecordItem {
    id: number;
    fishpond: number; // 鱼池
    growthinfo: number; // 生长周期ID
    record_date: string; // 记录日期
    in_out_type: string; // 类型（进、出）
    fishspecies: string; // 鱼品种
    fish_quantity: number; // 鱼数量
    fish_weight: number; // 鱼重量
    fish_specification: number; // 鱼规格(斤/条)
    unit_price?: number; // 销售单价
    money?: number; // 应收金额
    received_money?: number; // 实收金额
    salesman?: string[]; // 销售员
    sales_place?: string; // 销售地区
    remarks: string; // 备注
  }

  export type ResInoutrecord = ResPage<ResInoutrecordItem>;

  export interface ReqInoutrecordParam extends ReqPage {
    id: number;
    fishpond: number; // 鱼池
    growthinfo: number; // 生长周期ID
    record_date: string; // 记录日期
    in_out_type: string; // 类型（进、出）
    fishspecies: string; // 鱼品种
    fish_quantity: number; // 鱼数量
    fish_weight: number; // 鱼重量
    fish_specification: number; // 鱼规格(斤/条)
    unit_price?: number; // 销售单价
    money?: number; // 应收金额
    received_money?: number; // 实收金额
    salesman?: string[]; // 销售员
    sales_place?: string; // 销售地区
    remarks: string; // 备注
  }
}
