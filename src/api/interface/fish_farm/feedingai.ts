import { ReqPage, ResPage } from "@/api/interface";

// 投饵AI相关接口
export namespace FeedingAI {
  // 投饵事件列表
  export interface ResFeedingEventItem {
    id: number;
    pond: number; // 鱼池ID
    pond_name?: string; // 鱼池名称
    date: string; // 投饵日期时间
    fish_amount: number; // 鱼数量
    fish_batch: number; // 鱼批次
    continue_or_not: string; // 是否继续
    actual_feeding_amount: number; // 实际投饵量
    intensity_curve: Array<{
      time_offset_s: number; // 时间偏移（秒）
      intensity_level: number; // 强度等级
    }>; // 投饵强度曲线
    fishpond_id?: number; // 兼容字段
    fishpond_name?: string; // 兼容字段
    event_type?: string; // 事件类型
    event_time?: string; // 事件时间
    feeding_amount?: number; // 投饵量
    feeding_duration?: number; // 投饵时长
    fish_response?: string; // 鱼类反应
    water_quality_before?: any; // 投饵前水质
    water_quality_after?: any; // 投饵后水质
    ai_confidence?: number; // AI置信度
    manual_override?: boolean; // 人工干预
    notes?: string; // 备注
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
  }

  export type ResFeedingEvent = ResPage<ResFeedingEventItem>;

  export interface ReqFeedingEventParam extends ReqPage {
    fishpond_id?: number; // 鱼池ID
    event_type?: string; // 事件类型
    start_date?: string; // 开始日期
    end_date?: string; // 结束日期
    time_period?: string; // 时间段：morning, afternoon
  }

  // 生物量估算列表
  export interface ResBiomassEstimateItem {
    id: number;
    fishpond_id: number; // 鱼池ID
    fishpond_name?: string; // 鱼池名称
    estimate_date: string; // 估算日期
    estimated_biomass: number; // 估算生物量
    fish_count_estimate: number; // 估算鱼数量
    average_weight: number; // 平均重量
    confidence_level: number; // 置信度
    estimation_method: string; // 估算方法
    sensor_data: any; // 传感器数据
    image_analysis_data: any; // 图像分析数据
    historical_data_used: boolean; // 是否使用历史数据
    weather_conditions: any; // 天气条件
    water_temperature: number; // 水温
    notes: string; // 备注
    created_at: string; // 创建时间
    updated_at: string; // 更新时间
  }

  export type ResBiomassEstimate = ResPage<ResBiomassEstimateItem>;

  export interface ReqBiomassEstimateParam extends ReqPage {
    fishpond_id?: number; // 鱼池ID
    start_date?: string; // 开始日期
    end_date?: string; // 结束日期
    estimation_method?: string; // 估算方法
  }

  // 投饵统计
  export interface ResFeedingStatisticsItem {
    period: string; // 时间段（月份/周次）
    actual_amount: number; // 实际投饵量
    planned_amount: number; // 计划投饵量
    difference: number; // 差值
    start_date?: string; // 周统计时的起始日期
    end_date?: string; // 周统计时的结束日期
  }

  export interface ResFeedingStatistics {
    fishpond_id: string; // 鱼池ID
    fishpond_name: string; // 鱼池名称
    fish_quantity: number; // 鱼数量
    planned_daily_amount: number; // 日计划投饵量
    statistics_type: string; // 统计类型：yearly/monthly
    year: number; // 年份
    month?: number; // 月份（可选）
    data: ResFeedingStatisticsItem[]; // 统计数据
  }

  export interface ReqFeedingStatisticsParam {
    fishpond_id: number; // 鱼池ID（必填）
    year: number; // 年份，格式YYYY（必填）
    month?: number; // 月份，格式MM（可选）
  }

  // 历史投喂记录
  export interface ResFeedingHistoryItem {
    id: number;
    fish_amount: number; // 实际投喂量
    planned_amount: number; // 计划投喂量
    difference: number; // 偏差量
    date: string; // 投喂日期时间
    intensity_curve?: Array<{
      time_offset_s: number; // 时间偏移（秒）
      intensity_level: number; // 强度等级
    }>; // 投饵强度曲线
    pond_id?: number; // 鱼池ID
    pond_name?: string; // 鱼池名称
    feeding_duration?: number; // 投饵时长
    fish_response?: string; // 鱼类反应
    ai_confidence?: number; // AI置信度
    manual_override?: boolean; // 人工干预
    notes?: string; // 备注
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
  }

  export type ResFeedingHistory = ResPage<ResFeedingHistoryItem>;

  export interface ReqFeedingHistoryParam extends ReqPage {
    pond_id?: number; // 鱼池ID
    ordering?: string; // 排序方式，如：-date（按日期倒序）
    start_date?: string; // 开始日期
    end_date?: string; // 结束日期
  }
}
