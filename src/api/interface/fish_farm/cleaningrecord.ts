import { ReqPage, ResPage } from "@/api/interface";

// 清洗记录
export namespace FishFarm {
  // 养殖管理
  export interface ResCleaningrecordItem {
    id: number;
    fishpond: number; // 鱼池状态
    cleaning_date: string; // 清洗日期
    // cleaning_method: string; // 清洗方法
    remarks: string; // 备注
  }

  export type ResCleaningrecord = ResPage<ResCleaningrecordItem>;

  export interface ReqCleaningrecordParam extends ReqPage {
    id: number;
    fishpond: number; // 鱼池状态
    cleaning_date: string; // 清洗日期
    // cleaning_method: string; // 清洗方法
    remarks: string; // 备注
  }
}
