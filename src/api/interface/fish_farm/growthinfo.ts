import { ReqPage, ResPage } from "@/api/interface";

// 生长信息
export namespace FishFarm {
  // 养殖管理
  export interface ResGrowthinfoItem {
    id: number;
    fishfarmsystem: number; // 养鱼系统
    pici: string; // 批次
    tf_date: string; // 投放日期
    status: boolean; // 状态
    g_info: string; // 生长信息
  }

  export type ResGrowthinfo = ResPage<ResGrowthinfoItem>;

  export interface ReqGrowthinfoParam extends ReqPage {
    id: number;
    fishfarmsystem: number; // 养鱼系统
    pici: string; // 批次
    tf_date: string; // 投放日期
    status: boolean; // 状态
    g_info: string; // 生长信息
  }
}
