import { ReqPage, ResPage } from "@/api/interface";

// 筛分记录
export namespace FishFarm {
  // 养殖管理
  export interface ResSortingrecordItem {
    id: number;
    fishpond: number; // 鱼池
    sorting_date: string; // 筛分日期
    fish_quantity: number; // 鱼数量
    fish_weight: number; // 鱼重量
    fish_specifications: string; // 鱼规格
    remarks: string; // 备注
  }

  export type ResSortingrecord = ResPage<ResSortingrecordItem>;

  export interface ReqSortingrecordParam extends ReqPage {
    id: number;
    fishpond: number; // 鱼池
    sorting_date: string; // 筛分日期
    fish_quantity: number; // 鱼数量
    fish_weight: number; // 鱼重量
    fish_specifications: string; // 鱼规格
    remarks: string; // 备注
  }
}
