import { ReqPage, ResPage } from "@/api/interface";

// 喂药记录
export namespace FishFarm {
  // 养殖管理
  export interface ResMedicationrecordItem {
    id: number;
    fishpond: number; // 鱼池
    medication_date: string; // 喂药日期
    dosage: number; // 药量
    remarks: string; // 备注
    drug: number; // 药品名称
  }

  export type ResMedicationrecord = ResPage<ResMedicationrecordItem>;

  export interface ReqMedicationrecordParam extends ReqPage {
    id: number;
    fishpond: number; // 鱼池
    medication_date: string; // 喂药日期
    dosage: number; // 药量
    remarks: string; // 备注
    drug: number; // 药品名称
  }
}
