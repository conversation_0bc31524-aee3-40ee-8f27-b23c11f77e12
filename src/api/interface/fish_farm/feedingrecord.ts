import { ReqPage, ResPage } from "@/api/interface";

// 投饵记录
export namespace FishFarm {
  // 养殖管理
  export interface ResFeedingrecordItem {
    id: number;
    fishpond: number; // 鱼池
    feed_date: string; // 投饵日期
    feed_quantity: number; // 饵料数量
    remarks: string; // 备注
    assets: number; // 饵料名称
  }

  export type ResFeedingrecord = ResPage<ResFeedingrecordItem>;

  export interface ReqFeedingrecordParam extends ReqPage {
    id: number;
    fishpond: number; // 鱼池
    feed_date: string; // 投饵日期
    feed_quantity: number; // 饵料数量
    remarks: string; // 备注
    assets: number; // 饵料名称
  }
}
