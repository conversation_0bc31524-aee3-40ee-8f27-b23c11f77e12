import { ReqPage, ResPage } from "@/api/interface";

// 巡检位置
export namespace FishFarm {
  // 养殖管理
  export interface ResInspectlocationItem {
    id: number;
    name: string; // 巡检名称
    address: string; // 巡检位置
    device_name: string; // 设备名称
    image: string; // 图片
    alertsetting: string; // 更新频率
    qr_code: string; // 二维码
  }

  export type ResInspectlocation = ResPage<ResInspectlocationItem>;

  export interface ReqInspectlocationParam extends ReqPage {
    id: number;
    name: string; // 巡检名称
    address: string; // 巡检位置
    device_name: string; // 设备名称
    image: string; // 图片
    alertsetting: string; // 更新频率
    qr_code: string; // 二维码
  }
}
