import { ReqPage, ResPage } from "@/api/interface";

// 鱼池品种
export namespace FishFarm {
  // 养殖管理
  export interface ResFishspeciesItem {
    id: number;
    species_name: string; // 品种名称
    description: string; // 品种描述
  }

  export type ResFishspecies = ResPage<ResFishspeciesItem>;

  export interface ReqFishspeciesParam extends ReqPage {
    id: number;
    species_name: string; // 品种名称
    description: string; // 品种描述
  }
}
