import { ReqPage, ResPage } from "@/api/interface";

// 用电管理
export namespace Energy {
  // 能耗管理
  export interface ResPowermanagementItem {
    id: number;
    usage_amount: number; // 使用量(m³)
    usege_time: string; // 使用时间
  }

  export type ResPowermanagement = ResPage<ResPowermanagementItem>;

  export interface ReqPowermanagementParam extends ReqPage {
    id: number;
    usage_amount: number; // 使用量(m³)
    usege_time: string; // 使用时间
  }

  // 能耗管理统计
  export interface ResPowermanagementStatisticsItem {
    usage_amount: number[]; // 使用量
    usage_time: string[]; // 使用时间
  }

  // 最近六个月统计数据
  export interface ResPowermanagementSixMonthsStatisticsItem {
    usage_amount: number[]; // 使用量数组
    usage_time: string[]; // 使用时间数组
    period: string; // 统计周期
  }
}
