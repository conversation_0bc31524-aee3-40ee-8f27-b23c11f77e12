import { ReqPage, ResPage } from "@/api/interface";

// 用水管理
export namespace Energy {
  // 能耗管理
  export interface ResWatermanagementItem {
    id: number;
    usage_amount: number; // 使用量
    usege_time: string; // 使用时间
  }

  export type ResWatermanagement = ResPage<ResWatermanagementItem>;

  export interface ReqWatermanagementParam extends ReqPage {
    id: number;
    usage_amount: number; // 使用量
    usege_time: string; // 使用时间
  }

  export interface ResWatermanagementStatisticsItem {
    usage_amount: number[]; // 使用量
    usage_time: string[]; // 使用时间
  }

  // 最近六个月统计数据
  export interface ResWatermanagementSixMonthsStatisticsItem {
    usage_amount: number[]; // 使用量数组
    usage_time: string[]; // 使用时间数组
    period: string; // 统计周期
  }
}
