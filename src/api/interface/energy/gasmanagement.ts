import { ReqPage, ResPage } from "@/api/interface";

// 用气管理
export namespace Energy {
  // 能耗管理
  export interface ResGasmanagementItem {
    id: number;
    usage_amount: number; // 使用量
    usege_time: string; // 使用时间
  }

  export type ResGasmanagement = ResPage<ResGasmanagementItem>;

  export interface ReqGasmanagementParam extends ReqPage {
    id: number;
    usage_amount: number; // 使用量
    usege_time: string; // 使用时间
  }

  // 能耗管理统计
  export interface ResGasmanagementStatisticsItem {
    usage_amount: number[]; // 使用量
    usage_time: string[]; // 使用时间
  }

  // 最近六个月统计数据
  export interface ResGasmanagementSixMonthsStatisticsItem {
    usage_amount: number[]; // 使用量数组
    usage_time: string[]; // 使用时间数组
    period: string; // 统计周期
  }
}
