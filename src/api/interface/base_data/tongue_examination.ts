import { ReqPage, ResPage } from "@/api/interface";

// 舌象管理
export namespace BaseData {
  // 基础数据
  export interface ResTongueExaminationItem {
    id: number;
    name: string; // 舌象
    pinyin: string; // 舌象拼音
    tongue_examination_description: string; // 舌象描述
    order: number; // 排序
  }

  export type ResTongueExamination = ResPage<ResTongueExaminationItem>;

  export interface ReqTongueExaminationParam extends ReqPage {
    id: number;
    name: string; // 舌象
    pinyin: string; // 舌象拼音
    tongue_examination_description: string; // 舌象描述
    order: number; // 排序
  }
}
