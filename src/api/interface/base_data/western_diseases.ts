import { ReqPage, ResPage } from "@/api/interface";

// 西医诊断-病名
export namespace BaseData {
  // 基础数据
  export interface ResWesternDiseasesItem {
    id: number;
    name: string; // 西医病名
    pinyin: string; // 西医病名拼音
    western_diseases_description: string; // 概念
    order: number; // 排序
  }

  export type ResWesternDiseases = ResPage<ResWesternDiseasesItem>;

  export interface ReqWesternDiseasesParam extends ReqPage {
    id: number;
    name: string; // 西医病名
    pinyin: string; // 西医病名拼音
    western_diseases_description: string; // 概念
    order: number; // 排序
  }
}
