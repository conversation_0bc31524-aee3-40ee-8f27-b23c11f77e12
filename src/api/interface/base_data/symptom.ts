import { ReqPage, ResPage } from "@/api/interface";

// 症状管理
export namespace BaseData {
  // 基础数据
  export interface ResSymptomTree {
    id: number;
    name: string; // 症状名称
    pinyin: string; // 症状拼音
    symptom_tyc: string; // 同义词
    symptom_description: string; // 症状描述
    order: number; // 排序
    symptom: number; // 父节点
    children?: ResSymptomTree;
  }

  export type ResSymptom = ResPage<ResSymptomTree>;

  export interface ReqSymptomParam extends ReqPage {
    id?: number;
    name: string; // 症状名称
    pinyin: string; // 症状拼音
    symptom_tyc: string; // 同义词
    symptom_description: string; // 症状描述
    order: number; // 排序
    symptom: number | null; // 父节点
  }

  export interface SymptomNameTree {
    id?: number;
    name?: string;
    symptom?: number;
    children?: SymptomNameTree;
  }
}
