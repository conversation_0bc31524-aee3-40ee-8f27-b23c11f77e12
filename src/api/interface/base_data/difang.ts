import { ReqPage, ResPage } from "@/api/interface";

// 底方管理
export namespace BaseData {
  // 基础数据
  export interface ResDifangItem {
    id: number;
    name: string; // 底方名称
    pinyin: string; // 底方拼音
    tcm_prescription_description: string; // 描述
    order: number; // 排序
  }

  export type ResDifang = ResPage<ResDifangItem>;

  export interface ReqDifangParam extends ReqPage {
    id: number;
    name: string; // 底方名称
    pinyin: string; // 底方拼音
    tcm_prescription_description: string; // 描述
    order: number; // 排序
  }
}
