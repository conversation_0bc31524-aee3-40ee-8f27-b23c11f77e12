import { ReqPage, ResPage } from "@/api/interface";

// 中医诊断-证型
export namespace BaseData {
  // 基础数据
  export interface ResTcmSyndromeItem {
    id: number;
    name: string; // 中医证型
    pinyin: string; // 中医证型拼音
    tcm_syndrome_description: string; // 概念
    order: number; // 排序
  }

  export type ResTcmSyndrome = ResPage<ResTcmSyndromeItem>;

  export interface ReqTcmSyndromeParam extends ReqPage {
    id: number;
    name: string; // 中医证型
    pinyin: string; // 中医证型拼音
    tcm_syndrome_description: string; // 概念
    order: number; // 排序
  }
}
