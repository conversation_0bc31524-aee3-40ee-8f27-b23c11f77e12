import { ReqPage, ResPage } from "@/api/interface";

// 中医诊断-病名
export namespace BaseData {
  // 基础数据
  export interface ResTcmDiseasesItem {
    id: number;
    name: string; // 中医病名
    pinyin: string; // 中医病名拼音
    tcm_diseases_description: string; // 概念
    order: number; // 排序
  }

  export type ResTcmDiseases = ResPage<ResTcmDiseasesItem>;

  export interface ReqTcmDiseasesParam extends ReqPage {
    id: number;
    name: string; // 中医病名
    pinyin: string; // 中医病名拼音
    tcm_diseases_description: string; // 概念
    order: number; // 排序
  }
}
