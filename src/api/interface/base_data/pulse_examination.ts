import { ReqPage, ResPage } from "@/api/interface";

// 脉象管理
export namespace BaseData {
  // 基础数据
  export interface ResPulseExaminationItem {
    id: number;
    name: string; // 脉象
    pinyin: string; // 脉象拼音
    pulse_examination_description: string; // 脉象描述
    order: number; // 排序
  }

  export type ResPulseExamination = ResPage<ResPulseExaminationItem>;

  export interface ReqPulseExaminationParam extends ReqPage {
    id: number;
    name: string; // 脉象
    pinyin: string; // 脉象拼音
    pulse_examination_description: string; // 脉象描述
    order: number; // 排序
  }
}
