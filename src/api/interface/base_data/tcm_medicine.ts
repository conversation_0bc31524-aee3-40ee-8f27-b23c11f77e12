import { ReqPage, ResPage } from "@/api/interface";

// 中药数据
export namespace BaseData {
  // 基础数据
  export interface ResTcmMedicineItem {
    id: number;
    name: string; // 中药名称
    pinyin: string; // 中药拼音
    tcm_medicine_tyc: string; // 同义词
    tcm_medicine_from: string; // 中药来源
    tcm_medicine_location: string; // 药用部位
    tcm_medicine_location_st: string; // 药用部位标准
    tcm_medicine_sq: string; // 四气
    tcm_medicine_ww: string; // 五味
    tcm_medicine_gj: string; // 归经
    tcm_medicine_gx: string; // 功效
  }

  export type ResTcmMedicine = ResPage<ResTcmMedicineItem>;

  export interface ReqTcmMedicineParam extends ReqPage {
    id: number;
    name: string; // 中药名称
    pinyin: string; // 中药拼音
    tcm_medicine_tyc: string; // 同义词
    tcm_medicine_from: string; // 中药来源
    tcm_medicine_location: string; // 药用部位
    tcm_medicine_location_st: string; // 药用部位标准
    tcm_medicine_sq: string; // 四气
    tcm_medicine_ww: string; // 五味
    tcm_medicine_gj: string; // 归经
    tcm_medicine_gx: string; // 功效
  }
}
