import { ReqPage, ResPage } from "@/api/interface";

// 月经症状管理
export namespace BaseData {
  // 基础数据
  export interface ResSMenstrualHistoryItem {
    id: number;
    name: string; // 月经标准化症状
    pinyin: string; // 月经标准化症状拼音
    s_menstrual_history_description: string; // 月经标准化症状描述
    order: number; // 排序
  }

  export type ResSMenstrualHistory = ResPage<ResSMenstrualHistoryItem>;

  export interface ReqSMenstrualHistoryParam extends ReqPage {
    id: number;
    name: string; // 月经标准化症状
    pinyin: string; // 月经标准化症状拼音
    s_menstrual_history_description: string; // 月经标准化症状描述
    order: number; // 排序
  }
}
