import { ReqPage, ResPage } from "@/api/interface";

// 辅助检查参考值
export namespace BaseData {
  // 基础数据
  export interface ResChecklistItemItem {
    id: number;
    name: string; // 名称
    recipe_name: string; // 别名
    unit: string; // 单位
    min_range: any; // 参考最小值范围
    max_range: any; // 参考最大值范围
    order: number; // 排序
  }

  export type ResChecklistItem = ResPage<ResChecklistItemItem>;

  export interface ReqChecklistItemParam extends ReqPage {
    id: number;
    name: string; // 名称
    recipe_name: string; // 别名
    unit: string; // 单位
    min_range: any; // 参考最小值范围
    max_range: any; // 参考最大值范围
    order: number; // 排序
  }
}
