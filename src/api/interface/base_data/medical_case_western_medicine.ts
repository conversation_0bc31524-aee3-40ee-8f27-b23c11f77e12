import { ReqPage, ResPage } from "@/api/interface";

// 中西成药管理
export namespace BaseData {
  // 基础数据
  export interface ResMedicalCaseWesternMedicineItem {
    id: number;
    name: string; // 西药名称
    western_medicine_spm: string; // 西药商品名
    pinyin: string; // 西药拼音
    western_medicine_dw: string; // 单位
    western_medicine_ms: string; // 描述
    order: number; // 排序
  }

  export type ResMedicalCaseWesternMedicine = ResPage<ResMedicalCaseWesternMedicineItem>;

  export interface ReqMedicalCaseWesternMedicineParam extends ReqPage {
    id: number;
    name: string; // 西药名称
    western_medicine_spm: string; // 西药商品名
    pinyin: string; // 西药拼音
    western_medicine_dw: string; // 单位
    western_medicine_ms: string; // 描述
    order: number; // 排序
  }
}
