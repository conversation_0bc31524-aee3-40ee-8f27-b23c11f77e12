import { ReqPage, ResPage } from "@/api/interface";

// 养鱼系统
export namespace FarmSystem {
  // 养殖系统
  export interface ResFishfarmsystemItem {
    id: number;
    system_name: string; // 系统名
    system_description: string; // 系统描述
  }

  export type ResFishfarmsystem = ResPage<ResFishfarmsystemItem>;

  export interface ReqFishfarmsystemParam extends ReqPage {
    id: number;
    system_name: string; // 系统名
    system_description: string; // 系统描述
  }
}
