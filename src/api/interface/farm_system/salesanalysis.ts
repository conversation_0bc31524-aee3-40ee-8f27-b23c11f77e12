import { ReqPage, ResPage } from "@/api/interface";

// 销售分析
export namespace FarmSystem {
  // 养殖系统
  export interface ResSalesanalysisItem {
    id: number;
    month: string; // 月份
    market_price: number; // （元/斤）
  }

  export type ResSalesanalysis = ResPage<ResSalesanalysisItem>;

  export interface ReqSalesanalysisParam extends ReqPage {
    id: number;
    month: string; // 月份
    market_price: number; // （元/斤）
  }
}
