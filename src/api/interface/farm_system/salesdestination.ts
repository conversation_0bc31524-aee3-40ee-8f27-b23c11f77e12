import { ReqPage, ResPage } from "@/api/interface";

// 销售去向
export namespace FarmSystem {
  // 养殖系统
  export interface ResSalesdestinationItem {
    id: number;
    year: string; // 年份
    quantity: number; // 数量（斤）
    amount: number; // 金额（元）
    remarks: string; // 备注
    destination: string; // 去向
  }

  export type ResSalesdestination = ResPage<ResSalesdestinationItem>;

  export interface ReqSalesdestinationParam extends ReqPage {
    id: number;
    year: string; // 年份
    quantity: number; // 数量（斤）
    amount: number; // 金额（元）
    remarks: string; // 备注
    destination: string; // 去向
  }
}
