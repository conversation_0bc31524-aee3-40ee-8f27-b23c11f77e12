import { ReqPage, ResPage } from "@/api/interface";

// 批发零售分析
export namespace FarmSystem {
  // 养殖系统
  export interface ResWholesaleretailanalysisItem {
    id: number;
    year: string; // 年份
    wholesale_quantity: number; // 批发数量（斤）
    wholesale_amount: number; // 批发金额（元）
    retail_quantity: number; // 零售数量（斤）
    retail_amount: number; // 零售金额（元）
    remarks: string; // 备注
  }

  export type ResWholesaleretailanalysis = ResPage<ResWholesaleretailanalysisItem>;

  export interface ReqWholesaleretailanalysisParam extends ReqPage {
    id: number;
    year: string; // 年份
    wholesale_quantity: number; // 批发数量（斤）
    wholesale_amount: number; // 批发金额（元）
    retail_quantity: number; // 零售数量（斤）
    retail_amount: number; // 零售金额（元）
    remarks: string; // 备注
  }
}
