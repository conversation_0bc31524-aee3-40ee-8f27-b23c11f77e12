import { ReqPage, ResPage } from "@/api/interface";

// AI模型管理
export namespace AiModel {
  // AI模型项
  export interface ResAiModelItem {
    id: number;
    name: string; // 模型名称
    version: string; // 模型版本
    model_type: string; // 模型类型
    description: string; // 描述
    file_path: string; // 文件路径
    file_size: number; // 文件大小（字节）
    status: string; // 状态：online, offline, training, error
    accuracy?: number; // 准确率
    precision?: number; // 精确度
    recall?: number; // 召回率
    f1_score?: number; // F1分数
    training_dataset?: string; // 训练数据集
    validation_dataset?: string; // 验证数据集
    framework: string; // 框架：tensorflow, pytorch, sklearn等
    python_version?: string; // Python版本
    dependencies?: string; // 依赖包信息
    gpu_required?: boolean; // 是否需要GPU
    memory_requirement?: number; // 内存需求（MB）
    cpu_requirement?: number; // CPU需求
    tags?: string[]; // 标签
    author: string; // 作者
    created_by: number; // 创建人
    created_time: string; // 创建时间
    updated_time: string; // 更新时间
    last_used_time?: string; // 最后使用时间
    usage_count: number; // 使用次数
    download_count: number; // 下载次数
    is_public: boolean; // 是否公开
    license?: string; // 许可证
    dept_belong_id: string; // 数据归属部门
  }

  export type ResAiModel = ResPage<ResAiModelItem>;

  export interface ReqAiModelParam extends ReqPage {
    id?: number;
    name?: string; // 模型名称
    version?: string; // 模型版本
    model_type?: string; // 模型类型
    description?: string; // 描述
    file_path?: string; // 文件路径
    file_size?: number; // 文件大小
    status?: string; // 状态
    accuracy?: number; // 准确率
    precision?: number; // 精确度
    recall?: number; // 召回率
    f1_score?: number; // F1分数
    training_dataset?: string; // 训练数据集
    validation_dataset?: string; // 验证数据集
    framework?: string; // 框架
    python_version?: string; // Python版本
    dependencies?: string; // 依赖包信息
    gpu_required?: boolean; // 是否需要GPU
    memory_requirement?: number; // 内存需求
    cpu_requirement?: number; // CPU需求
    tags?: string[]; // 标签
    author?: string; // 作者
    is_public?: boolean; // 是否公开
    license?: string; // 许可证
  }

  // 模型类型字典
  export interface ModelTypeDict {
    [key: string]: string;
  }

  // 状态选择项
  export interface StatusChoice {
    value: string;
    label: string;
  }

  // 统计信息
  export interface Statistics {
    total_models: number;
    online_models: number;
    offline_models: number;
    training_models: number;
    error_models: number;
    total_usage: number;
    total_downloads: number;
    avg_accuracy: number;
    storage_used: number; // 使用的存储空间（字节）
  }

  // 使用记录请求
  export interface ReqRecordUsage {
    usage_duration?: number; // 使用时长（秒）
    input_tokens?: number; // 输入token数
    output_tokens?: number; // 输出token数
    success: boolean; // 是否成功
    error_message?: string; // 错误信息
    user_id?: number; // 用户ID
    session_id?: string; // 会话ID
  }

  // 批量操作请求
  export interface ReqBatchOperation {
    ids: number[];
  }

  // 下载请求
  export interface ReqDownload {
    format?: string; // 下载格式
    include_dependencies?: boolean; // 是否包含依赖
  }

  // 搜索请求
  export interface ReqSearch {
    keyword?: string; // 关键词
    model_type?: string; // 模型类型
    framework?: string; // 框架
    status?: string; // 状态
    min_accuracy?: number; // 最小准确率
    max_accuracy?: number; // 最大准确率
    tags?: string[]; // 标签
    author?: string; // 作者
    is_public?: boolean; // 是否公开
  }

  // 模型选项（用于下拉选择）
  export interface AiModelOption {
    value: number;
    label: string;
    version?: string;
    model_type?: string;
    status?: string;
  }
}
