import { ReqPage, ResPage } from "@/api/interface";

// 消息类型
export namespace FishMessage {
  // 鱼菜消息
  export interface ResMessagetypeItem {
    id: number;
    name: string; // 名字
    icon: any; // 图标
    description: string; // 描述
  }

  export type ResMessagetype = ResPage<ResMessagetypeItem>;

  export interface ReqMessagetypeParam extends ReqPage {
    id: number;
    name: string; // 名字
    icon: any; // 图标
    description: string; // 描述
  }
}
