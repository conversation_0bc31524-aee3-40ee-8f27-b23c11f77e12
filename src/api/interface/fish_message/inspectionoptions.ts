import { ReqPage, ResPage } from "@/api/interface";

// 预警配置选项
export namespace FishMessage {
  // 鱼菜消息
  export interface ResInspectionoptionsItem {
    id: number;
    name: string; // 配置名
    label: string;
    minute: any; // 每时第几分钟
    hour: any; // 第几小时
    week: any; // 第几星期
    day: any; // 第几号
  }

  export type ResInspectionoptions = ResPage<ResInspectionoptionsItem>;

  export interface ReqInspectionoptionsParam extends ReqPage {
    id: number;
    name: string; // 配置名
    label?: string;
    minute: any; // 每时第几分钟
    hour: any; // 第几小时
    week: any; // 第几星期
    day: any; // 第几号
  }
}
