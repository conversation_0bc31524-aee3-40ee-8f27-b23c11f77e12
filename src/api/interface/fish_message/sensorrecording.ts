import { ReqPage, ResPage } from "@/api/interface";

// 传感器指标
export namespace FishMessage {
  // 鱼菜消息
  export interface ResSensorrecordingItem {
    id: number;
    fishpond: number; // 鱼池状态
    cur_count: number; // 当前提醒次数
    max_count: number; // 当天最大提醒次数
    name: string; // 传感器名称
    top: number; // 上限
    bottom: number; // 下限
    status: boolean; // 状态
    unit: string; // 单位
  }

  export type ResSensorrecording = ResPage<ResSensorrecordingItem>;

  export interface ReqSensorrecordingParam extends ReqPage {
    id: number;
    fishpond: number; // 鱼池状态
    cur_count: number; // 当前提醒次数
    max_count: number; // 当天最大提醒次数
    name: string; // 传感器名称
    top: number; // 上限
    bottom: number; // 下限
    status: boolean; // 状态
    unit: string; // 单位
  }
}
